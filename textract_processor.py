import json
import time
import boto3
import os
import logging
from typing import <PERSON><PERSON>, Dict, Optional
from botocore.exceptions import ClientError


def process_file_with_textract(
    file_path: str,
    region: str = 'us-east-1',
    bucket_name: Optional[str] = None,
    temp_prefix: str = 'temp-textract',
    logger: Optional[logging.Logger] = None
) -> Tuple[Dict, str, str]:
    """
    Process a local file with AWS Textract and return the raw response, formatted text, and reconstructed layout.

    Args:
        file_path (str): Local path to the file to process
        region (str): AWS region for Textract service
        bucket_name (Optional[str]): S3 bucket name for temporary file upload
        temp_prefix (str): S3 prefix for temporary files
        logger (Optional[logging.Logger]): Logger instance

    Returns:
        Tuple[Dict, str, str]: (textract_response, formatted_text_with_coordinates, reconstructed_layout)

    Example formatted text output:
        === TEXT WITH COORDINATES ===
        text, x1, y1, x2, y2
        SmartWay, 0.4509, 0.0311, 0.5840, 0.0538
        TRANSPORTATION, 0.4522, 0.0514, 0.6103, 0.0654
        ...

    Example reconstructed layout output:
        === RECONSTRUCTED LAYOUT ===
        SmartWay                    Invoice #: 12345
        TRANSPORTATION              Date: 01/15/2024
        ...
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    # Initialize AWS clients
    textract_client = boto3.client('textract', region_name=region)
    s3_client = boto3.client('s3', region_name=region)
    
    if bucket_name is None:
        raise ValueError("bucket_name is required for S3 upload")
    
    try:
        # Step 1: Upload file to S3
        file_name = os.path.basename(file_path)
        s3_key = f"{temp_prefix}/{file_name}"
        logger.info(f"Uploading {file_path} to s3://{bucket_name}/{s3_key}")
        
        s3_client.upload_file(file_path, bucket_name, s3_key)
        s3_uri = f"s3://{bucket_name}/{s3_key}"
        logger.info(f"Successfully uploaded to {s3_uri}")
        
        # Step 2: Invoke Textract
        textract_response = _invoke_textract(textract_client, s3_uri, logger)
        
        # Step 3: Get final results
        textract_results = _check_textract_job_status(textract_client, textract_response, logger)
        
        # Step 4: Convert to formatted text
        formatted_text = _convert_textract_to_structured_format(textract_results, logger)

        # Step 5: Generate reconstructed layout
        reconstructed_layout = _reconstruct_layout_from_coordinates(textract_results, logger)

        # Step 6: Clean up S3 file (optional)
        try:
            s3_client.delete_object(Bucket=bucket_name, Key=s3_key)
            logger.info(f"Cleaned up temporary S3 file: {s3_uri}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to clean up S3 file: {cleanup_error}")

        return textract_results, formatted_text, reconstructed_layout
        
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}")
        raise


def _invoke_textract(textract_client, s3_uri: str, logger) -> dict:
    """Invoke Textract to extract text from document."""
    try:
        # Parse S3 URI to get bucket and key
        s3_parts = s3_uri.replace('s3://', '').split('/', 1)
        bucket = s3_parts[0]
        key = s3_parts[1]
        
        logger.info(f"Starting Textract text extraction for {s3_uri}")
        
        # Always try synchronous detection first for faster processing
        try:
            logger.info("Attempting synchronous Textract text detection")
            response = textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            logger.info("Synchronous Textract text detection successful")
            return {'sync_response': response, 'is_sync': True}
        except ClientError as sync_error:
            # Check if it's a document size error (common reason for sync failure)
            error_code = sync_error.response.get('Error', {}).get('Code', '')
            if error_code in ['InvalidParameterException', 'UnsupportedDocumentException']:
                logger.info(f"Document too large for sync processing ({error_code}), using async")
            else:
                logger.warning(f"Synchronous Textract failed: {sync_error}")
            logger.info("Falling back to asynchronous Textract")
        
        # Asynchronous text detection - only text, no tables or forms for speed
        response = textract_client.start_document_text_detection(
            DocumentLocation={
                'S3Object': {
                    'Bucket': bucket,
                    'Name': key
                }
            }
            # No FeatureTypes specified = text only (fastest and cheapest)
        )
        
        job_id = response['JobId']
        logger.info(f"Textract async text detection job started with ID: {job_id}")
        
        return {'JobId': job_id, 'is_sync': False}
    except ClientError as e:
        logger.error(f"Error starting Textract job: {e}")
        raise


def _check_textract_job_status(textract_client, textract_response: dict, logger) -> dict:
    """Check the status of the Textract job or return sync response."""
    try:
        # Handle synchronous response
        if textract_response.get('is_sync'):
            logger.info("Using synchronous Textract response")
            return textract_response['sync_response']
        
        # Handle asynchronous response
        job_id = textract_response.get('JobId')
        if not job_id:
            raise ValueError("No JobId found in Textract response")
        
        logger.info(f"Monitoring Textract job status for ID: {job_id}")
        while True:
            response = textract_client.get_document_text_detection(JobId=job_id)
            status = response.get('JobStatus')
            logger.info(f"Textract job status: {status}")
            
            if status in ['SUCCEEDED', 'FAILED']:
                logger.info(f"Textract job completed with status: {status}")
                return response
            elif status == 'IN_PROGRESS':
                # Reduced sleep time for faster polling
                time.sleep(5)
            else:
                raise ValueError(f"Unexpected job status: {status}")
    except ClientError as e:
        logger.error(f"Error checking Textract job status: {e}")
        raise


def _convert_textract_to_structured_format(textract_response: dict, logger) -> str:
    """Convert Textract response to structured text format for LLM processing."""
    try:
        blocks = textract_response.get('Blocks', [])

        # Extract text blocks with coordinates in the required format
        text_lines = []

        # Process LINE blocks for text with coordinates (most efficient)
        for block in blocks:
            if block['BlockType'] == 'LINE':
                bbox = block.get('Geometry', {}).get('BoundingBox', {})
                text = block.get('Text', '')

                # Properly escape text for CSV format - enclose in quotes if contains comma
                if ',' in text:
                    # Escape any existing quotes by doubling them, then wrap in quotes
                    escaped_text = text.replace('"', '""')
                    text = f'"{escaped_text}"'

                # Convert to x1, y1, x2, y2 format
                x1 = bbox.get('Left', 0)
                y1 = bbox.get('Top', 0)
                x2 = x1 + bbox.get('Width', 0)
                y2 = y1 + bbox.get('Height', 0)

                text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

        # If no LINE blocks found (sync response), use WORD blocks
        if not text_lines:
            logger.info("No LINE blocks found, using WORD blocks for text extraction")
            for block in blocks:
                if block['BlockType'] == 'WORD':
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '')

                    # Properly escape text for CSV format - enclose in quotes if contains comma
                    if ',' in text:
                        # Escape any existing quotes by doubling them, then wrap in quotes
                        escaped_text = text.replace('"', '""')
                        text = f'"{escaped_text}"'

                    # Convert to x1, y1, x2, y2 format
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)

                    text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

        # Build the structured text format (text only, no tables for speed)
        structured_text_parts = []

        # Add header
        structured_text_parts.append("=== TEXT WITH COORDINATES ===")
        structured_text_parts.append("text, x1, y1, x2, y2")

        # Add text lines
        for line in text_lines:
            structured_text_parts.append(line)

        structured_text = "\n".join(structured_text_parts)

        logger.info(f"Extracted {len(text_lines)} text lines from document")
        return structured_text

    except Exception as e:
        logger.error(f"Error converting Textract response to structured format: {e}")
        raise


def _reconstruct_layout_from_coordinates(textract_response: dict, logger) -> str:
    """
    Reconstruct the document layout from bounding box coordinates to preserve column structure.

    This function spatially organizes text elements based on their coordinates to maintain
    the original document layout, helping AI models distinguish between different columns
    and sections without mixing up values.

    Args:
        textract_response (dict): Textract response containing blocks with coordinates
        logger: Logger instance

    Returns:
        str: Reconstructed layout text that preserves spatial relationships
    """
    try:
        blocks = textract_response.get('Blocks', [])

        # Extract text elements with their coordinates
        text_elements = []

        # Process LINE blocks first (preferred for layout reconstruction)
        for block in blocks:
            if block['BlockType'] == 'LINE':
                bbox = block.get('Geometry', {}).get('BoundingBox', {})
                text = block.get('Text', '').strip()

                if text:  # Only include non-empty text
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)
                    width = bbox.get('Width', 0)
                    height = bbox.get('Height', 0)

                    text_elements.append({
                        'text': text,
                        'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
                        'width': width, 'height': height,
                        'center_x': x1 + width / 2,
                        'center_y': y1 + height / 2
                    })

        # If no LINE blocks found, use WORD blocks and group them into lines
        if not text_elements:
            logger.info("No LINE blocks found, reconstructing layout from WORD blocks")
            word_elements = []

            for block in blocks:
                if block['BlockType'] == 'WORD':
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '').strip()

                    if text:
                        x1 = bbox.get('Left', 0)
                        y1 = bbox.get('Top', 0)
                        width = bbox.get('Width', 0)
                        height = bbox.get('Height', 0)
                        x2 = x1 + width
                        y2 = y1 + height

                        word_elements.append({
                            'text': text,
                            'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
                            'width': width, 'height': height,
                            'center_x': x1 + width / 2,
                            'center_y': y1 + height / 2
                        })

            # Group words into lines based on vertical proximity
            text_elements = _group_words_into_lines_advanced(word_elements)

        if not text_elements:
            logger.warning("No text elements found for layout reconstruction")
            return "=== RECONSTRUCTED LAYOUT ===\n[No text content found]"

        # Sort elements by vertical position (top to bottom)
        text_elements.sort(key=lambda x: x['y1'])

        # Create a character-based grid layout
        reconstructed_layout = _create_character_grid_layout(text_elements, logger)

        logger.info(f"Reconstructed layout from {len(text_elements)} text elements")
        return reconstructed_layout

    except Exception as e:
        logger.error(f"Error reconstructing layout from coordinates: {e}")
        return f"=== RECONSTRUCTED LAYOUT ===\n[Error reconstructing layout: {e}]"


def _group_words_into_lines_advanced(word_elements: list) -> list:
    """Advanced grouping of individual words into lines with better accuracy."""
    if not word_elements:
        return []

    # Sort words by vertical position first, then horizontal
    word_elements.sort(key=lambda x: (x['y1'], x['x1']))

    lines = []
    current_line_words = [word_elements[0]]

    for word in word_elements[1:]:
        # Check if this word is on the same line as the previous words
        # Use more sophisticated line detection
        current_line_y_range = (
            min(w['y1'] for w in current_line_words),
            max(w['y2'] for w in current_line_words)
        )
        current_line_height = current_line_y_range[1] - current_line_y_range[0]

        # Check if word overlaps vertically with current line
        word_y_range = (word['y1'], word['y2'])

        # Calculate overlap
        overlap_start = max(current_line_y_range[0], word_y_range[0])
        overlap_end = min(current_line_y_range[1], word_y_range[1])
        overlap = max(0, overlap_end - overlap_start)

        # If there's significant vertical overlap, it's likely the same line
        min_height = min(current_line_height, word['height'])
        if overlap >= min_height * 0.3:  # 30% overlap threshold
            current_line_words.append(word)
        else:
            # Create a line element from current words
            if current_line_words:
                line_element = _merge_words_into_line_advanced(current_line_words)
                lines.append(line_element)
            current_line_words = [word]

    # Add the last line
    if current_line_words:
        line_element = _merge_words_into_line_advanced(current_line_words)
        lines.append(line_element)

    return lines


def _merge_words_into_line_advanced(words: list) -> dict:
    """Merge multiple words into a single line element with preserved spacing."""
    if not words:
        return {}

    # Sort words by horizontal position
    words.sort(key=lambda x: x['x1'])

    # Calculate bounding box for the entire line
    x1 = min(word['x1'] for word in words)
    y1 = min(word['y1'] for word in words)
    x2 = max(word['x2'] for word in words)
    y2 = max(word['y2'] for word in words)
    width = x2 - x1
    height = y2 - y1

    return {
        'text': ' '.join(word['text'] for word in words),
        'words': words,  # Keep individual words for spacing calculation
        'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
        'width': width, 'height': height,
        'center_x': x1 + width / 2,
        'center_y': y1 + height / 2
    }


def _create_character_grid_layout(text_elements: list, logger=None) -> str:
    """
    Create a character-based grid layout that accurately preserves document structure.

    This method creates a virtual character grid and places text elements based on
    their coordinates, ensuring proper alignment and spacing.
    """
    if not text_elements:
        return "=== RECONSTRUCTED LAYOUT ===\n[No text content found]"

    # Analyze document structure first
    doc_analysis = _analyze_document_structure(text_elements)

    # Define grid parameters based on analysis
    GRID_WIDTH = 150  # Increased width for better layout
    CHAR_WIDTH = 1.0 / GRID_WIDTH  # Normalized character width

    # Use analyzed line height for better accuracy
    avg_line_height = doc_analysis['avg_line_height']
    min_y = doc_analysis['min_y']

    # Create grid with better conflict resolution
    grid = {}  # y_position -> {x_position: character}
    line_metadata = {}  # y_position -> metadata about the line

    # Process each text element
    for elem in text_elements:
        # Calculate grid position with better precision
        relative_y = elem['y1'] - min_y
        grid_y = round(relative_y / avg_line_height)

        # Ensure we don't have negative positions
        grid_y = max(0, grid_y)

        # Calculate horizontal position with better accuracy
        grid_x = round(elem['x1'] / CHAR_WIDTH)
        grid_x = max(0, grid_x)

        # Initialize grid row if needed
        if grid_y not in grid:
            grid[grid_y] = {}
            line_metadata[grid_y] = {'elements': []}

        line_metadata[grid_y]['elements'].append(elem)

        # Handle text placement with conflict resolution
        text = elem['text']

        # Check for conflicts and resolve them
        for i, char in enumerate(text):
            pos_x = grid_x + i
            if pos_x < GRID_WIDTH:
                # If position is already occupied, try to find nearby space
                if pos_x in grid[grid_y]:
                    # Look for next available position
                    for offset in range(1, 5):
                        if pos_x + offset not in grid[grid_y] and pos_x + offset < GRID_WIDTH:
                            pos_x = pos_x + offset
                            break
                    else:
                        # If no space found, skip this character
                        continue

                grid[grid_y][pos_x] = char

    # Post-process to improve layout
    grid = _post_process_grid(grid, line_metadata, GRID_WIDTH)

    # Convert grid to text
    layout_lines = ["=== RECONSTRUCTED LAYOUT ==="]

    # Sort by y position and create lines
    for y in sorted(grid.keys()):
        line_chars = [' '] * GRID_WIDTH

        # Fill in characters
        for x, char in grid[y].items():
            if 0 <= x < GRID_WIDTH:
                line_chars[x] = char

        # Convert to string and remove trailing spaces
        line = ''.join(line_chars).rstrip()
        if line.strip():  # Only add non-empty lines
            layout_lines.append(line)

    return '\n'.join(layout_lines)


def _analyze_document_structure(text_elements: list) -> dict:
    """Analyze document structure to determine optimal grid parameters."""
    if not text_elements:
        return {'avg_line_height': 0.02, 'min_y': 0, 'max_y': 1}

    # Calculate basic statistics
    min_y = min(elem['y1'] for elem in text_elements)
    max_y = max(elem['y2'] for elem in text_elements)

    # Group elements by approximate lines to calculate line height
    sorted_elements = sorted(text_elements, key=lambda x: x['y1'])
    line_heights = []

    for i in range(len(sorted_elements) - 1):
        current = sorted_elements[i]
        next_elem = sorted_elements[i + 1]

        # If elements don't overlap vertically, calculate gap
        if current['y2'] <= next_elem['y1']:
            gap = next_elem['y1'] - current['y2']
            if gap > 0:
                line_heights.append(current['height'] + gap)

    # Calculate average line height
    if line_heights:
        avg_line_height = sum(line_heights) / len(line_heights)
    else:
        avg_line_height = sum(elem['height'] for elem in text_elements) / len(text_elements)

    # Ensure minimum line height
    avg_line_height = max(avg_line_height, 0.015)

    return {
        'avg_line_height': avg_line_height,
        'min_y': min_y,
        'max_y': max_y,
        'doc_height': max_y - min_y
    }


def _post_process_grid(grid: dict, line_metadata: dict, grid_width: int) -> dict:
    """Post-process the grid to improve layout quality."""

    # Sort elements within each line by x-coordinate and ensure proper spacing
    for y, metadata in line_metadata.items():
        if y not in grid:
            continue

        elements = metadata['elements']
        if len(elements) <= 1:
            continue

        # Sort elements by x-coordinate
        elements.sort(key=lambda x: x['x1'])

        # Clear the line and rebuild with better spacing and conflict resolution
        grid[y] = {}

        for i, elem in enumerate(elements):
            # Calculate position with better spacing
            grid_x = round(elem['x1'] * grid_width)
            grid_x = max(0, grid_x)

            # Check for conflicts with previous elements in the same line
            if i > 0:
                prev_elem = elements[i - 1]
                prev_end_x = round(prev_elem['x2'] * grid_width)

                # Ensure minimum spacing between elements
                min_gap = 2  # Minimum 2 characters gap
                if grid_x < prev_end_x + min_gap:
                    grid_x = prev_end_x + min_gap

            # Place text with conflict resolution
            text = elem['text']
            for j, char in enumerate(text):
                pos_x = grid_x + j
                if pos_x < grid_width:
                    # Check if position is already occupied
                    if pos_x in grid[y]:
                        # Find next available position
                        while pos_x in grid[y] and pos_x < grid_width:
                            pos_x += 1
                        if pos_x >= grid_width:
                            break

                    grid[y][pos_x] = char

    return grid


# Example usage
if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Example usage
    file_path = "/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data/invoice_shortlisted_v2/Unishippers _ Worldwide Express/processed/250802W002752.pdf"
    bucket_name = "document-extraction-logistically"
    
    try:
        textract_response, formatted_text, reconstructed_layout = process_file_with_textract(
            file_path=file_path,
            bucket_name=bucket_name,
            logger=logger
        )

        print("=== TEXTRACT RESPONSE ===")
        print(json.dumps(textract_response, indent=2, default=str))

        print("\n=== FORMATTED TEXT WITH COORDINATES ===")
        print(formatted_text)

        print("\n=== RECONSTRUCTED LAYOUT ===")
        print(reconstructed_layout)
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
