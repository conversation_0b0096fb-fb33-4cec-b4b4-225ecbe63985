import os
import re
import logging
import shutil
import tempfile
from concurrent.futures import ThreadPoolExecutor, as_completed

import boto3
import pandas as pd

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)


INVALID_CHARS = re.compile(r'[<>:"/\\|?*]')                 # pre-compile once


def _sanitize(name: str) -> str:
    """Replace characters that are illegal in file names on most file systems."""
    return INVALID_CHARS.sub("_", str(name))


def _safe_save_csv(df: pd.DataFrame, path: str, columns: list) -> None:
    """
    Safely save a DataFrame to a CSV file using a temporary file, then atomically replace.
    This prevents truncation or corruption if interrupted mid-save.
    """
    dirpath = os.path.dirname(path)
    with tempfile.NamedTemporaryFile(mode='w', dir=dirpath, delete=False, newline='', suffix='.csv.tmp') as tmpfile:
        tmp_path = tmpfile.name
        df.to_csv(tmp_path, index=False, columns=columns)
        tmpfile.flush()
        os.fsync(tmpfile.fileno())  # Ensure fully written to disk
    shutil.move(tmp_path, path)     # Atomic replace
    logging.info(f"Safely saved CSV to {path} via temp file")
    



def download_s3_files(csv_path: str,
                      base_folder: str,
                      num_files: int | None = None,
                      bucket: str = "tms-upload-prod",
                      max_workers: int = 20) -> None:
    """
    Download the files listed in a CSV produced by TRUX.  
    The CSV is rewritten *without* losing or re-ordering any original columns or rows.
    Saves are atomic to prevent corruption on interrupts.
    """

    # ------------------------------------------------------------------ #
    # 1. Load the CSV and remember the original column order and row count
    # ------------------------------------------------------------------ #
    try:
        df = pd.read_csv(csv_path)
    except Exception as exc:
        logging.error("Unable to read CSV: %s", exc)
        return

    original_cols = df.columns.tolist()             # exact order on disk
    original_row_count = len(df)
    logging.info("Loaded CSV with %s rows and columns: %s", original_row_count, original_cols)

    # ------------------------------------------------------------------ #
    # 2. Add download_status column if it doesn't exist
    #    and guarantee it is *appended* (never replace / reorder)
    # ------------------------------------------------------------------ #
    if "download_status" not in df.columns:
        df["download_status"] = ""                  # create blank column
        original_cols.append("download_status")     # remember new order

    # For safety always re-index to preserved order
    df = df.reindex(columns=original_cols)

    # ------------------------------------------------------------------ #
    # 3. Pick the rows that still need attention (but never modify the original df's rows)
    # ------------------------------------------------------------------ #
    pending_mask = df["download_status"].ne("downloaded")
    pending_rows = df.loc[pending_mask].copy()  # Use a copy to avoid any view/modification issues

    if num_files:
        pending_rows = pending_rows.head(num_files)

    if pending_rows.empty:
        logging.info("Everything already downloaded — nothing to do.")
        return

    # ------------------------------------------------------------------ #
    # 4. Set up AWS S3 client once
    # ------------------------------------------------------------------ #
    s3 = boto3.client("s3")

    # ------------------------------------------------------------------ #
    # 5. Worker that will be executed in threads
    # ------------------------------------------------------------------ #
    def _process_row(idx: int, row: pd.Series) -> str:
        """
        Return value:
        'downloaded'      – just downloaded successfully
        'already_exists'  – file was already present locally
        'error: <text>'   – anything else
        """
        try:
            key          = row["path_to_file"]
            fname        = _sanitize(row["original_file_name"])
            carrier_name = _sanitize(row["carrier_name"])

            subfolder = {
                "invoice": "invoice",
                "combined_carrier_documents": "combined_carrier_documents"
            }.get(row["attachment_type"], "other")

            local_folder = os.path.join(base_folder,
                                        _sanitize(subfolder),
                                        carrier_name)
            os.makedirs(local_folder, exist_ok=True)

            local_path = os.path.join(local_folder, fname)

            # ---------------------------------------------------------- #
            # Short-circuit if we already have the complete file
            # ---------------------------------------------------------- #
            if os.path.exists(local_path):
                return "already_exists"

            # ---------------------------------------------------------- #
            # Download via temp name → rename (avoids half files)
            # ---------------------------------------------------------- #
            tmp_path = f"{local_path}.tmp"
            if os.path.exists(tmp_path):     # stale temp from crash
                os.remove(tmp_path)

            logging.info("Downloading s3://%s/%s → %s", bucket, key, local_path)
            s3.download_file(bucket, key, tmp_path)
            os.rename(tmp_path, local_path)

            return "downloaded"

        except Exception as exc:
            logging.error("Error while processing %s: %s",
                          row.get("original_file_name", "<?>"), exc)
            # Best-effort cleanup of partial download
            if "tmp_path" in locals() and os.path.exists(tmp_path):
                os.remove(tmp_path)
            return f"error: {exc}"

    # ------------------------------------------------------------------ #
    # 6. Kick off threaded downloads
    # ------------------------------------------------------------------ #
    stats = {"downloaded": 0, "already_exists": 0, "errors": 0}

    try:
        with ThreadPoolExecutor(max_workers=max_workers) as pool:
            futures = {
                pool.submit(_process_row, idx, row): idx
                for idx, row in pending_rows.iterrows()
            }

            for future in as_completed(futures):
                idx = futures[future]
                result = future.result()

                if result == "downloaded":
                    stats["downloaded"] += 1
                    df.at[idx, "download_status"] = "downloaded"

                elif result == "already_exists":
                    stats["already_exists"] += 1
                    df.at[idx, "download_status"] = "downloaded"

                else:                                        # error path
                    stats["errors"] += 1
                    # keep whatever message we got for post-mortem analysis
                    df.at[idx, "download_status"] = result

                # ---------------------------------------------------------- #
                # 7. Persist CSV after each row so that the job is resumable
                #    (preserve original column order and all rows every time)
                # ---------------------------------------------------------- #
                try:
                    _safe_save_csv(df, csv_path, original_cols)
                    # Verify row count after save
                    temp_df = pd.read_csv(csv_path)
                    if len(temp_df) != original_row_count:
                        logging.error("Row count mismatch after save! Original: %s, After: %s", original_row_count, len(temp_df))
                except Exception as exc:
                    logging.error("Unable to save CSV after row %s: %s", idx, exc)

    except KeyboardInterrupt:
        logging.warning("KeyboardInterrupt detected. Saving current progress safely...")
        try:
            _safe_save_csv(df, csv_path, original_cols)
            logging.info("Progress saved. You can resume by re-running the script.")
        except Exception as exc:
            logging.error("Error during interrupt save: %s", exc)
        finally:
            raise  # Re-raise to exit cleanly

    # ------------------------------------------------------------------ #
    # 8. Final full save and verification
    # ------------------------------------------------------------------ #
    try:
        _safe_save_csv(df, csv_path, original_cols)
        final_df = pd.read_csv(csv_path)
        if len(final_df) != original_row_count:
            logging.error("Final row count mismatch! Original: %s, Final: %s", original_row_count, len(final_df))
        else:
            logging.info("Final CSV saved with all %s rows intact.", original_row_count)
    except Exception as exc:
        logging.error("Unable to perform final CSV save: %s", exc)

    # ------------------------------------------------------------------ #
    # 9. Summary
    # ------------------------------------------------------------------ #
    logging.info(
        "Completed. Downloaded=%s, AlreadyPresent=%s, Errors=%s",
        stats["downloaded"], stats["already_exists"], stats["errors"]
    )


download_s3_files(csv_path='/home/<USER>/Documents/repositories/logistically/docs/logistically_attachment_data_with_invoice_info.csv', 
                  base_folder='/home/<USER>/Documents/repositories/logistically/data/input_data/10k_w_true_data', 
                  num_files=1)