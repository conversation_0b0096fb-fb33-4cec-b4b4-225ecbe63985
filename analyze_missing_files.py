#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze which files are missing from the evaluation and why.
This will help identify the remaining 307 files that weren't evaluated.
"""

import os
import pandas as pd
import json
import glob
from pathlib import Path

def load_tracking_data():
    """Load the file tracking CSV."""
    tracking_file = "data/output_data/extraction/file_tracking.csv"
    if not os.path.exists(tracking_file):
        print(f"❌ Tracking file not found: {tracking_file}")
        return None
    
    df = pd.read_csv(tracking_file)
    print(f"📊 Total files in tracking: {len(df)}")
    return df

def load_true_data():
    """Load the true data CSV."""
    true_data_file = "docs/logistically_attachment_data_with_invoice_info.csv"
    if not os.path.exists(true_data_file):
        print(f"❌ True data file not found: {true_data_file}")
        return None
    
    df = pd.read_csv(true_data_file)
    print(f"📊 Total rows in true data: {len(df)}")
    return df

def find_extracted_files():
    """Find all extracted JSON files."""
    extracted_dir = "data/output_data/extraction"
    
    patterns = [
        "*_output_result.json",
        "*_extracted.json",
        "*_*_extracted.json",
        "*_*_extracted_v*.json"
    ]
    
    all_files = []
    for pattern in patterns:
        files = glob.glob(os.path.join(extracted_dir, pattern))
        all_files.extend(files)
    
    # Remove duplicates and filter out subdirectories
    unique_files = []
    for file_path in set(all_files):
        if os.path.dirname(file_path) == extracted_dir:
            unique_files.append(file_path)
    
    print(f"📊 Total extracted JSON files: {len(unique_files)}")
    return unique_files

def extract_original_filename_from_extracted(extracted_filename):
    """Extract the original filename from the extracted filename."""
    import re
    
    # Remove .json extension
    base_name = extracted_filename.replace('.json', '')
    
    # Remove version suffix if present (e.g., _v2, _v3)
    base_name = re.sub(r'_v\d+$', '', base_name)
    
    # Remove _extracted suffix
    base_name = base_name.replace('_extracted', '')
    
    # If there's still an underscore followed by what looks like an invoice number, remove it
    parts = base_name.split('_')
    if len(parts) > 1:
        last_part = parts[-1]
        
        # Check if the last part looks like an invoice number
        is_likely_invoice = (
            any(c.isdigit() for c in last_part) and
            re.match(r'^[A-Za-z0-9_-]+$', last_part) and
            len(last_part) > 1 and
            not last_part.isalpha() and
            not last_part.startswith('NOINV')
        )
        
        if last_part.startswith('NOINV_'):
            is_likely_invoice = True
        
        if is_likely_invoice:
            base_name = '_'.join(parts[:-1])
    
    return base_name

def analyze_missing_files():
    """Analyze which files are missing from evaluation and why."""
    print("🔍 Analyzing Missing Files from Evaluation")
    print("="*60)
    
    # Load data
    tracking_df = load_tracking_data()
    true_data_df = load_true_data()
    extracted_files = find_extracted_files()
    
    if tracking_df is None or true_data_df is None:
        print("❌ Cannot proceed without required data files")
        return
    
    print(f"\n📈 Summary:")
    print(f"  - Files in tracking CSV: {len(tracking_df)}")
    print(f"  - Files in true data CSV: {len(true_data_df)}")
    print(f"  - Extracted JSON files: {len(extracted_files)}")
    print(f"  - Successfully evaluated: 1057")
    print(f"  - Missing from evaluation: {len(extracted_files) - 1057}")
    
    # Analyze each extracted file
    missing_files = []
    invoice_not_found = []
    filename_not_found = []
    no_tracking_data = []
    
    for extracted_file in extracted_files:
        extracted_filename = os.path.basename(extracted_file)
        
        # Extract base name
        if extracted_filename.endswith("_output_result.json"):
            base_name = extracted_filename.replace("_output_result.json", "")
        else:
            base_name = extract_original_filename_from_extracted(extracted_filename)
        
        # Load the JSON file to get invoice number
        try:
            with open(extracted_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            invoice_number = data.get('invoice_number', '')
        except Exception as e:
            print(f"❌ Error loading {extracted_filename}: {e}")
            continue
        
        # Check if invoice number exists in true data
        invoice_match = False
        if invoice_number and str(invoice_number).strip():
            clean_invoice = str(invoice_number).strip()
            invoice_matches = true_data_df[true_data_df['invoice_number'].astype(str).str.strip() == clean_invoice]
            invoice_match = not invoice_matches.empty
        
        # Check if filename can be found via tracking data
        filename_match = False
        tracking_info = None
        for _, row in tracking_df.iterrows():
            json_filename = row.get('extracted_json_filename', '')
            if json_filename == extracted_filename:
                tracking_info = row
                pdf_filename = row.get('pdf_filename', '')
                if pdf_filename:
                    pdf_base = os.path.splitext(pdf_filename)[0]
                    filename_matches = true_data_df[true_data_df['original_file_name'].astype(str).str.contains(pdf_base, case=False, na=False)]
                    filename_match = not filename_matches.empty
                break
        
        # Categorize the file
        if not invoice_match and not filename_match:
            if tracking_info is None:
                no_tracking_data.append({
                    'file': extracted_filename,
                    'base_name': base_name,
                    'invoice_number': invoice_number,
                    'reason': 'No tracking data found'
                })
            elif not invoice_match and not filename_match:
                pdf_filename = tracking_info.get('pdf_filename', '') if hasattr(tracking_info, 'get') else ''
                missing_files.append({
                    'file': extracted_filename,
                    'base_name': base_name,
                    'invoice_number': invoice_number,
                    'pdf_filename': pdf_filename,
                    'reason': 'Neither invoice number nor filename found in true data'
                })
        elif not invoice_match:
            pdf_filename = tracking_info.get('pdf_filename', '') if hasattr(tracking_info, 'get') else ''
            invoice_not_found.append({
                'file': extracted_filename,
                'base_name': base_name,
                'invoice_number': invoice_number,
                'pdf_filename': pdf_filename,
                'reason': 'Invoice number not found in true data (but filename might match)'
            })
        elif not filename_match:
            pdf_filename = tracking_info.get('pdf_filename', '') if hasattr(tracking_info, 'get') else ''
            filename_not_found.append({
                'file': extracted_filename,
                'base_name': base_name,
                'invoice_number': invoice_number,
                'pdf_filename': pdf_filename,
                'reason': 'Filename not found in true data (but invoice number matches)'
            })
    
    print(f"\n🔍 Analysis Results:")
    print(f"  - Files with no tracking data: {len(no_tracking_data)}")
    print(f"  - Files with invoice number not in true data: {len(invoice_not_found)}")
    print(f"  - Files with filename not in true data: {len(filename_not_found)}")
    print(f"  - Files completely missing from true data: {len(missing_files)}")
    
    # Show examples
    if no_tracking_data:
        print(f"\n📋 Files with no tracking data (first 10):")
        for item in no_tracking_data[:10]:
            print(f"  - {item['file']} (invoice: {item['invoice_number']})")
    
    if invoice_not_found:
        print(f"\n📋 Files with invoice number not in true data (first 10):")
        for item in invoice_not_found[:10]:
            print(f"  - {item['file']} (invoice: {item['invoice_number']}, pdf: {item['pdf_filename']})")
    
    if missing_files:
        print(f"\n📋 Files completely missing from true data (first 10):")
        for item in missing_files[:10]:
            print(f"  - {item['file']} (invoice: {item['invoice_number']}, pdf: {item['pdf_filename']})")
    
    # Save detailed analysis
    analysis_file = "data/evaluation/missing_files_analysis.json"
    os.makedirs(os.path.dirname(analysis_file), exist_ok=True)
    
    analysis_data = {
        'summary': {
            'total_extracted_files': len(extracted_files),
            'successfully_evaluated': 1057,
            'missing_from_evaluation': len(extracted_files) - 1057,
            'no_tracking_data': len(no_tracking_data),
            'invoice_not_found': len(invoice_not_found),
            'filename_not_found': len(filename_not_found),
            'completely_missing': len(missing_files)
        },
        'no_tracking_data': no_tracking_data,
        'invoice_not_found': invoice_not_found,
        'filename_not_found': filename_not_found,
        'completely_missing': missing_files
    }
    
    with open(analysis_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Detailed analysis saved to: {analysis_file}")
    
    return analysis_data

if __name__ == "__main__":
    analyze_missing_files()
