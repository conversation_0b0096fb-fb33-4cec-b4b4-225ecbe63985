#!/usr/bin/env python3
"""
Debug script to understand why the evaluation is not processing all files.
"""

import sys
import os
import glob
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from evaluation_extraction import find_extracted_files

def debug_file_discovery():
    """Debug the file discovery process."""
    print("🔍 Debugging File Discovery Process")
    print("="*50)
    
    extracted_dir = "data/output_data/extraction"
    
    # Check what the evaluation script finds
    print(f"\n📁 Checking directory: {extracted_dir}")
    print(f"📁 Directory exists: {os.path.exists(extracted_dir)}")
    
    if os.path.exists(extracted_dir):
        all_files = os.listdir(extracted_dir)
        print(f"📁 Total files in directory: {len(all_files)}")
        
        # Count different file types
        json_files = [f for f in all_files if f.endswith('.json')]
        csv_files = [f for f in all_files if f.endswith('.csv')]
        other_files = [f for f in all_files if not f.endswith('.json') and not f.endswith('.csv')]
        
        print(f"📁 JSON files: {len(json_files)}")
        print(f"📁 CSV files: {len(csv_files)}")
        print(f"📁 Other files: {len(other_files)}")
        
        # Show some examples
        print(f"\n📋 First 10 JSON files:")
        for i, f in enumerate(json_files[:10]):
            print(f"  {i+1}. {f}")
    
    # Use the evaluation script's file discovery
    print(f"\n🔍 Using evaluation script's find_extracted_files():")
    try:
        extracted_files = find_extracted_files(extracted_dir)
        print(f"📊 Files found by evaluation script: {len(extracted_files)}")
        
        if extracted_files:
            print(f"\n📋 First 10 files found by evaluation script:")
            for i, f in enumerate(extracted_files[:10]):
                filename = os.path.basename(f)
                print(f"  {i+1}. {filename}")
        
        # Check for specific patterns
        patterns_found = {}
        for file_path in extracted_files:
            filename = os.path.basename(file_path)
            if "_extracted.json" in filename:
                patterns_found["_extracted.json"] = patterns_found.get("_extracted.json", 0) + 1
            elif "_extracted_v" in filename:
                patterns_found["_extracted_v*.json"] = patterns_found.get("_extracted_v*.json", 0) + 1
            elif "_output_result.json" in filename:
                patterns_found["_output_result.json"] = patterns_found.get("_output_result.json", 0) + 1
            else:
                patterns_found["other"] = patterns_found.get("other", 0) + 1
        
        print(f"\n📊 Pattern breakdown:")
        for pattern, count in patterns_found.items():
            print(f"  - {pattern}: {count}")
            
    except Exception as e:
        print(f"❌ Error using evaluation script's file discovery: {e}")
    
    # Manual glob patterns
    print(f"\n🔍 Using manual glob patterns:")
    patterns = [
        "*_output_result.json",
        "*_extracted.json",
        "*_*_extracted.json",
        "*_*_extracted_v*.json"
    ]
    
    all_manual_files = []
    for pattern in patterns:
        files = glob.glob(os.path.join(extracted_dir, pattern))
        print(f"  - Pattern '{pattern}': {len(files)} files")
        all_manual_files.extend(files)
    
    # Remove duplicates
    unique_manual_files = list(set(all_manual_files))
    print(f"📊 Total unique files from manual patterns: {len(unique_manual_files)}")
    
    # Compare the two methods
    if 'extracted_files' in locals():
        eval_set = set(os.path.basename(f) for f in extracted_files)
        manual_set = set(os.path.basename(f) for f in unique_manual_files)
        
        only_in_eval = eval_set - manual_set
        only_in_manual = manual_set - eval_set
        
        print(f"\n🔍 Comparison:")
        print(f"  - Files only found by evaluation script: {len(only_in_eval)}")
        print(f"  - Files only found by manual patterns: {len(only_in_manual)}")
        
        if only_in_manual:
            print(f"\n📋 Files only found by manual patterns (first 10):")
            for i, f in enumerate(list(only_in_manual)[:10]):
                print(f"  {i+1}. {f}")

def main():
    debug_file_discovery()

if __name__ == "__main__":
    main()
