#!/usr/bin/env python3
"""
Run the full evaluation with detailed logging to understand why not all files are processed.
"""

import sys
import os
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from evaluation_extraction import run_csv_evaluation

def setup_detailed_logging():
    """Set up detailed logging."""
    # Create logs directory if it doesn't exist
    os.makedirs("data/logs", exist_ok=True)
    
    # Set up file logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("data/logs/full_evaluation_debug.log", mode='w'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    return logger

def run_full_evaluation():
    """Run the full evaluation with detailed logging."""
    logger = setup_detailed_logging()
    
    print("🚀 Running Full Evaluation with Detailed Logging")
    print("="*60)
    
    # Run the evaluation
    result = run_csv_evaluation(
        csv_file_path="docs/logistically_attachment_data_with_invoice_info.csv",
        extracted_dir="data/output_data/extraction",
        output_file="data/evaluation/full_evaluation_with_all_cases.xlsx"
    )
    
    if result:
        print(f"\n✅ Evaluation completed successfully!")
        print(f"📊 Report saved to: {result['output_file']}")
        print(f"📁 Total files evaluated: {result['total_files']}")
        
        print(f"\n📈 Summary Statistics:")
        for field_name, stats in result['summary'].items():
            total = stats['total_values']
            correct = stats['correct']
            null_count = stats['null_count']
            true_data_not_found = stats.get('true_data_not_found_count', 0)
            comparable = stats['comparable']
            accuracy = stats['accuracy']
            
            print(f"  {field_name}:")
            print(f"    Total: {total}")
            print(f"    Correct: {correct}")
            print(f"    Wrong: {total - correct - null_count - true_data_not_found}")
            print(f"    NULL (No CSV Match): {null_count}")
            print(f"    TRUE DATA NOT FOUND: {true_data_not_found}")
            print(f"    Accuracy: {accuracy:.1f}%")
            print()
        
        # Calculate files with different mapping methods
        print(f"\n🔍 Key Insights:")
        print(f"  - Files processed: {result['total_files']}")
        print(f"  - Expected files: 1364")
        print(f"  - Files missing: {1364 - result['total_files']}")
        print(f"  - Check the Excel report for 'NOT MAPPED FROM INVOICE NUMBER' column")
        print(f"  - 'YES' in that column indicates files processed with fallback logic")
        print(f"  - 'TRUE DATA NOT FOUND' indicates files where no matching data exists in CSV")
        
        # Check log file
        log_file = "data/logs/full_evaluation_debug.log"
        if os.path.exists(log_file):
            print(f"\n📋 Detailed logs saved to: {log_file}")
            
            # Count errors in log file
            with open(log_file, 'r') as f:
                log_content = f.read()
                error_count = log_content.count('ERROR')
                warning_count = log_content.count('WARNING')
                
            print(f"📊 Log summary: {error_count} errors, {warning_count} warnings")
        
    else:
        print("❌ Evaluation failed!")
        return False
    
    return True

def main():
    """Main function."""
    success = run_full_evaluation()
    
    if success:
        print(f"\n✅ Full evaluation completed!")
        print(f"\n📋 Next Steps:")
        print(f"  1. Open the Excel report: data/evaluation/full_evaluation_with_all_cases.xlsx")
        print(f"  2. Filter by 'NOT MAPPED FROM INVOICE NUMBER' = 'YES' to see fallback-processed files")
        print(f"  3. Look for 'TRUE DATA NOT FOUND' entries to identify files without CSV matches")
        print(f"  4. Check the debug log for any errors: data/logs/full_evaluation_debug.log")
    else:
        print(f"❌ Evaluation failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
